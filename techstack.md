# Technology Stack

## Core Framework
- **Vite** - Build tool and development server
- **React 18** - Frontend framework with TypeScript
- **TypeScript** - Type-safe JavaScript

## UI & Styling
- **Tailwind CSS** - Utility-first CSS framework
- **shadcn/ui** - Component library built on Radix UI
- **Radix UI** - Headless UI primitives
- **Lucide React** - Icon library

## State & Data
- **TanStack Query** - Server state management
- **React Hook Form** - Form handling with Zod validation
- **React Router DOM** - Client-side routing

## Development Tools
- **ESLint** - Code linting
- **PostCSS** - CSS processing
- **Lovable** - AI-powered development platform

## Project Purpose
**Ringerike Landskap Tri-fold Brochure** - Premium digital brochure showcasing landscaping services in Ringerike municipality, featuring elegant design with gradient overlays, glassmorphism effects, and responsive layout.
