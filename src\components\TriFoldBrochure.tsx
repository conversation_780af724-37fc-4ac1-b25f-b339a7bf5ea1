import { Leaf, TreePine, Droplets, Shovel, Phone, Mail, MapPin, Globe, Star } from "lucide-react";
import BrochurePanel from "./BrochurePanel";
import ServiceCard from "./ServiceCard";
import { Button } from "@/components/ui/button";
import heroImage from "@/assets/hero-landscape.jpg";

const TriFoldBrochure = () => {
  return (
    <div className="w-full max-w-6xl mx-auto bg-white shadow-2xl overflow-hidden rounded-lg">
      <div className="grid grid-cols-3 gap-0 h-[400px] aspect-[3/1]">
        
        {/* Left Panel - Premium Front Cover */}
        <BrochurePanel type="front" className="relative overflow-hidden">
          {/* Premium gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-emerald-500 via-emerald-600 to-green-600 opacity-95" />

          {/* Sophisticated background patterns */}
          <div className="absolute inset-0">
            <div className="absolute top-0 right-0 w-32 h-32 bg-white/5 rounded-full transform translate-x-16 -translate-y-16 blur-2xl" />
            <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/5 rounded-full transform -translate-x-12 translate-y-12 blur-xl" />
          </div>

          <div className="relative z-10 flex flex-col justify-center items-center h-full text-center p-4">
            {/* Premium logo design */}
            <div className="relative mb-4">
              <div className="w-16 h-16 bg-white/15 rounded-full mx-auto flex items-center justify-center backdrop-blur-md border border-white/20 shadow-xl">
                <Leaf className="w-8 h-8 text-white" />
              </div>
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-white/25 rounded-full" />
            </div>

            <h1 className="font-playfair text-3xl font-bold mb-1 tracking-wide text-white drop-shadow-lg">
              Ringerike
            </h1>
            <h2 className="font-inter text-lg font-light mb-3 tracking-[0.2em] text-white/95">
              LANDSKAP
            </h2>

            <div className="w-12 h-px bg-white/60 mx-auto mb-3" />

            <p className="font-inter text-xs text-white/90 max-w-[200px] leading-relaxed font-light mb-4">
              Eksklusiv hagedesign og landskapspleie i Ringerike kommune
            </p>

            <div className="space-y-1 text-xs">
              <div className="flex items-center justify-center gap-2 bg-white/10 backdrop-blur-md rounded-full px-3 py-1 border border-white/20">
                <Phone className="w-3 h-3" />
                <span className="font-inter font-medium">+47 123 45 678</span>
              </div>
              <div className="flex items-center justify-center gap-2 bg-white/10 backdrop-blur-md rounded-full px-3 py-1 border border-white/20">
                <Globe className="w-3 h-3" />
                <span className="font-inter font-medium">ringerikelandskap.no</span>
              </div>
            </div>
          </div>
        </BrochurePanel>

        {/* Center Panel - Premium About */}
        <BrochurePanel type="inside" className="relative">
          {/* Premium brown gradient */}
          <div className="absolute inset-0 bg-gradient-to-br from-orange-600 via-orange-700 to-amber-800 opacity-95" />

          {/* Sophisticated background elements */}
          <div className="absolute inset-0">
            <div className="absolute top-0 right-0 w-24 h-24 bg-white/5 rounded-full transform translate-x-12 -translate-y-12 blur-xl" />
            <div className="absolute bottom-0 left-0 w-20 h-20 bg-white/5 rounded-full transform -translate-x-10 translate-y-10 blur-lg" />
          </div>

          <div className="relative z-10 p-4 flex flex-col justify-between h-full">
            <div>
              <div className="flex items-center gap-2 mb-3">
                <div className="w-6 h-1 bg-white/80 rounded-full" />
                <h2 className="font-playfair text-2xl font-bold text-white">Om oss</h2>
              </div>

              <div className="space-y-2 mb-4">
                <div className="bg-white/10 backdrop-blur-md p-3 rounded-xl border border-white/20 shadow-lg">
                  <div className="flex items-start gap-2">
                    <div className="w-1.5 h-1.5 bg-white/80 rounded-full mt-1 flex-shrink-0" />
                    <div>
                      <h3 className="font-inter font-semibold text-white text-xs mb-1">15+ års erfaring</h3>
                      <p className="font-inter text-white/90 text-xs leading-tight">
                        Ringerike Landskap har over 15 års erfaring med eksklusiv hagedesign
                        og landskapspleie.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-white/10 backdrop-blur-md p-3 rounded-xl border border-white/20 shadow-lg">
                  <div className="flex items-start gap-2">
                    <div className="w-1.5 h-1.5 bg-white/80 rounded-full mt-1 flex-shrink-0" />
                    <div>
                      <h3 className="font-inter font-semibold text-white text-xs mb-1">Bærekraftige løsninger</h3>
                      <p className="font-inter text-white/90 text-xs leading-tight">
                        Vårt team fokuserer på miljøvennlige løsninger med lokale planter.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 gap-2">
                <div className="group hover:bg-white/5 p-2 rounded-lg transition-all duration-300">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-gradient-to-br from-white/20 to-white/10 rounded-lg flex items-center justify-center shadow-xl backdrop-blur-md">
                      <TreePine className="w-4 h-4 text-white" />
                    </div>
                    <div>
                      <h4 className="font-inter font-bold text-white text-xs mb-0.5">Profesjonell design</h4>
                      <p className="font-inter text-white/80 text-xs">Skreddersydde løsninger</p>
                    </div>
                  </div>
                </div>

                <div className="group hover:bg-white/5 p-2 rounded-lg transition-all duration-300">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-gradient-to-br from-white/20 to-white/10 rounded-lg flex items-center justify-center shadow-xl backdrop-blur-md">
                      <Star className="w-4 h-4 text-white" />
                    </div>
                    <div>
                      <h4 className="font-inter font-bold text-white text-xs mb-0.5">Førsteklasses kvalitet</h4>
                      <p className="font-inter text-white/80 text-xs">Garantert høy standard</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-auto">
              <div className="bg-gradient-to-r from-white/15 via-white/20 to-white/15 text-white p-3 rounded-xl shadow-xl backdrop-blur-md border border-white/20">
                <h3 className="font-playfair text-lg font-bold mb-1">Kontakt oss</h3>
                <p className="font-inter text-xs mb-2 text-white/90">Ring for eksklusiv konsultasjon</p>
                <div className="space-y-1 text-xs">
                  <div className="flex items-center gap-2 bg-white/10 backdrop-blur-md rounded-full px-2 py-1 border border-white/20">
                    <Phone className="w-3 h-3" />
                    <span className="font-inter font-medium">+47 123 45 678</span>
                  </div>
                  <div className="flex items-center gap-2 bg-white/10 backdrop-blur-md rounded-full px-2 py-1 border border-white/20">
                    <Mail className="w-3 h-3" />
                    <span className="font-inter font-medium"><EMAIL></span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </BrochurePanel>

          <div className="mt-auto p-6 relative z-10">
            <div className="bg-gradient-to-r from-white/15 via-white/20 to-white/15 text-white p-4 rounded-2xl shadow-2xl backdrop-blur-md border border-white/20 relative overflow-hidden">
              <div className="absolute top-0 right-0 w-16 h-16 bg-white/5 rounded-full transform translate-x-8 -translate-y-8" />

              <div className="relative z-10">
                <h3 className="font-playfair text-xl font-bold mb-2">Kontakt oss</h3>
                <p className="font-inter text-xs mb-3 text-white/90">Ring for eksklusiv konsultasjon</p>
                <div className="space-y-2 text-xs">
                  <div className="flex items-center gap-2 bg-white/10 backdrop-blur-md rounded-full px-3 py-2 border border-white/20">
                    <Phone className="w-3 h-3" />
                    <span className="font-inter font-medium">+47 123 45 678</span>
                  </div>
                  <div className="flex items-center gap-2 bg-white/10 backdrop-blur-md rounded-full px-3 py-2 border border-white/20">
                    <Mail className="w-3 h-3" />
                    <span className="font-inter font-medium"><EMAIL></span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </BrochurePanel>

        {/* Right Panel - Premium Services */}
        <BrochurePanel type="back" className="relative">
          {/* Premium gray gradient */}
          <div className="absolute inset-0 bg-gradient-to-br from-slate-600 via-slate-700 to-gray-800 opacity-95" />

          {/* Sophisticated background elements */}
          <div className="absolute inset-0">
            <div className="absolute top-0 left-0 w-24 h-24 bg-white/5 rounded-full transform -translate-x-12 -translate-y-12 blur-xl" />
            <div className="absolute bottom-1/3 right-0 w-20 h-20 bg-white/5 rounded-full transform translate-x-10 blur-lg" />
          </div>

          <div className="relative z-10 p-4 flex flex-col justify-between h-full">
            <div>
              <div className="flex items-center gap-2 mb-3">
                <h2 className="font-playfair text-2xl font-bold text-white">Våre tjenester</h2>
                <div className="w-6 h-1 bg-white/80 rounded-full" />
              </div>

              <div className="grid grid-cols-2 gap-2 mb-4">
                <div className="group hover:scale-105 transition-transform duration-300">
                  <div className="bg-white/10 backdrop-blur-md p-3 rounded-xl text-center border border-white/20 hover:border-white/40 transition-all shadow-lg">
                    <div className="w-12 h-12 bg-gradient-to-br from-white/20 to-white/10 rounded-xl flex items-center justify-center mx-auto mb-2 shadow-xl">
                      <TreePine className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="font-inter font-bold text-white text-xs">Hagedesign</h3>
                  </div>
                </div>

                <div className="group hover:scale-105 transition-transform duration-300">
                  <div className="bg-white/10 backdrop-blur-md p-3 rounded-xl text-center border border-white/20 hover:border-white/40 transition-all shadow-lg">
                    <div className="w-12 h-12 bg-gradient-to-br from-white/20 to-white/10 rounded-xl flex items-center justify-center mx-auto mb-2 shadow-xl">
                      <Leaf className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="font-inter font-bold text-white text-xs">Planting & Stell</h3>
                  </div>
                </div>

                <div className="group hover:scale-105 transition-transform duration-300">
                  <div className="bg-white/10 backdrop-blur-md p-3 rounded-xl text-center border border-white/20 hover:border-white/40 transition-all shadow-lg">
                    <div className="w-12 h-12 bg-gradient-to-br from-white/20 to-white/10 rounded-xl flex items-center justify-center mx-auto mb-2 shadow-xl">
                      <Droplets className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="font-inter font-bold text-white text-xs">Vannanlegg</h3>
                  </div>
                </div>

                <div className="group hover:scale-105 transition-transform duration-300">
                  <div className="bg-white/10 backdrop-blur-md p-3 rounded-xl text-center border border-white/20 hover:border-white/40 transition-all shadow-lg">
                    <div className="w-12 h-12 bg-gradient-to-br from-white/20 to-white/10 rounded-xl flex items-center justify-center mx-auto mb-2 shadow-xl">
                      <Shovel className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="font-inter font-bold text-white text-xs">Gravearbeider</h3>
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-auto">
              <div className="bg-gradient-to-r from-white/15 via-white/20 to-white/15 text-white p-3 rounded-xl shadow-xl backdrop-blur-md border border-white/20 relative overflow-hidden">
                <div className="absolute top-0 left-0 w-16 h-16 bg-white/5 rounded-full transform -translate-x-8 -translate-y-8" />

                <div className="relative z-10">
                  <h3 className="font-playfair text-lg font-bold mb-1">Gratis konsultasjon</h3>
                  <p className="font-inter text-xs mb-3 text-white/90">Vi kommer hjem til deg og lager et eksklusivt tilbud</p>
                  <Button
                    variant="secondary"
                    className="w-full bg-white/90 text-slate-800 hover:bg-white font-inter font-semibold py-2 text-xs rounded-xl shadow-xl hover:shadow-2xl transition-all transform hover:scale-105"
                  >
                    Bestill konsultasjon
                  </Button>
                </div>
              </div>

              <div className="mt-2 flex items-center justify-center gap-2 text-xs text-white/80 bg-white/10 backdrop-blur-md rounded-full px-3 py-2 border border-white/20">
                <MapPin className="w-3 h-3" />
                <span className="font-inter font-medium">Dekker hele Ringerike kommune</span>
              </div>
            </div>
          </div>
        </BrochurePanel>
      </div>
    </div>
  );
};

export default TriFoldBrochure;