import { Leaf, TreePine, Droplets, Shovel, Phone, Mail, MapPin, Globe, Star } from "lucide-react";
import BrochurePanel from "./BrochurePanel";
import ServiceCard from "./ServiceCard";
import { Button } from "@/components/ui/button";
import heroImage from "@/assets/hero-landscape.jpg";

const TriFoldBrochure = () => {
  return (
    <div className="w-full max-w-7xl mx-auto bg-white shadow-2xl overflow-hidden rounded-2xl">
      <div className="grid grid-cols-3 gap-0 h-[500px]">
        
        {/* Left Panel - Premium Front Cover */}
        <BrochurePanel type="front" className="relative overflow-hidden">
          {/* Premium gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-emerald-600 via-emerald-500 to-teal-500 opacity-95" />
          
          {/* Sophisticated background patterns */}
          <div className="absolute inset-0">
            <div className="absolute top-0 right-0 w-96 h-96 bg-white/5 rounded-full transform translate-x-48 -translate-y-48 blur-3xl" />
            <div className="absolute bottom-0 left-0 w-64 h-64 bg-white/5 rounded-full transform -translate-x-32 translate-y-32 blur-2xl" />
            <div className="absolute top-1/2 left-1/2 w-40 h-40 bg-white/5 rounded-full transform -translate-x-20 -translate-y-20 blur-xl" />
          </div>
          
          {/* Elegant geometric lines */}
          <div className="absolute inset-0">
            <div className="absolute top-1/4 left-0 w-full h-px bg-gradient-to-r from-transparent via-white/20 to-transparent" />
            <div className="absolute bottom-1/4 left-0 w-full h-px bg-gradient-to-r from-transparent via-white/20 to-transparent" />
            <div className="absolute top-0 left-1/4 w-px h-full bg-gradient-to-b from-transparent via-white/10 to-transparent" />
          </div>
          
          <div className="relative z-10 flex flex-col justify-center items-center h-full text-center p-6">
            <div className="mb-6">
              {/* Premium logo design */}
              <div className="relative mb-6">
                <div className="w-20 h-20 bg-white/15 rounded-full mx-auto flex items-center justify-center backdrop-blur-md border border-white/20 shadow-xl">
                  <Leaf className="w-10 h-10 text-white" />
                </div>
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-white/25 rounded-full" />
                <div className="absolute -bottom-1 -left-1 w-4 h-4 bg-white/30 rounded-full" />
              </div>

              <h1 className="font-playfair text-4xl font-bold mb-2 tracking-wide text-white drop-shadow-lg">
                Ringerike
              </h1>
              <h2 className="font-inter text-2xl font-light mb-4 tracking-[0.2em] text-white/95">
                LANDSKAP
              </h2>

              <div className="w-16 h-px bg-white/60 mx-auto mb-4" />

              <p className="font-inter text-sm text-white/90 max-w-xs leading-relaxed font-light">
                Eksklusiv hagedesign og landskapspleie i Ringerike kommune
              </p>
            </div>

            <div className="mt-auto space-y-2 text-xs">
              <div className="flex items-center justify-center gap-2 bg-white/10 backdrop-blur-md rounded-full px-4 py-2 border border-white/20">
                <Phone className="w-3 h-3" />
                <span className="font-inter font-medium">+47 123 45 678</span>
              </div>
              <div className="flex items-center justify-center gap-2 bg-white/10 backdrop-blur-md rounded-full px-4 py-2 border border-white/20">
                <Globe className="w-3 h-3" />
                <span className="font-inter font-medium">www.ringerikelandskap.no</span>
              </div>
            </div>
          </div>
        </BrochurePanel>

        {/* Center Panel - Premium About */}
        <BrochurePanel type="inside" className="relative">
          {/* Premium brown gradient */}
          <div className="absolute inset-0 bg-gradient-to-br from-amber-800 via-amber-700 to-orange-800 opacity-95" />
          
          {/* Sophisticated background elements */}
          <div className="absolute inset-0">
            <div className="absolute top-0 right-0 w-80 h-80 bg-white/5 rounded-full transform translate-x-40 -translate-y-40 blur-3xl" />
            <div className="absolute bottom-0 left-0 w-60 h-60 bg-white/5 rounded-full transform -translate-x-30 translate-y-30 blur-2xl" />
          </div>
          
          <div className="relative z-10 p-6">
            <div className="mb-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-8 h-1 bg-white/80 rounded-full" />
                <h2 className="font-playfair text-3xl font-bold text-white">Om oss</h2>
              </div>
            </div>

            <div className="space-y-4 mb-6">
              <div className="bg-white/10 backdrop-blur-md p-4 rounded-2xl border border-white/20 shadow-lg">
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-white/80 rounded-full mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="font-inter font-semibold text-white text-sm mb-1">15+ års erfaring</h3>
                    <p className="font-inter text-white/90 text-xs leading-relaxed">
                      Ringerike Landskap har over 15 års erfaring med eksklusiv hagedesign
                      og landskapspleie. Vi skaper unike utendørsrom som reflekterer naturens skjønnhet.
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white/10 backdrop-blur-md p-4 rounded-2xl border border-white/20 shadow-lg">
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-white/80 rounded-full mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="font-inter font-semibold text-white text-sm mb-1">Bærekraftige løsninger</h3>
                    <p className="font-inter text-white/90 text-xs leading-relaxed">
                      Vårt team av sertifiserte landskapsarkitekter fokuserer på miljøvennlige
                      løsninger med lokale planter og smarte vanningssystemer.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 gap-3">
              <div className="group hover:bg-white/5 p-3 rounded-xl transition-all duration-300">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-white/20 to-white/10 rounded-xl flex items-center justify-center shadow-xl backdrop-blur-md">
                    <TreePine className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h4 className="font-inter font-bold text-white text-sm mb-1">Profesjonell design</h4>
                    <p className="font-inter text-white/80 text-xs">Skreddersydde løsninger for hver kunde</p>
                  </div>
                </div>
              </div>

              <div className="group hover:bg-white/5 p-3 rounded-xl transition-all duration-300">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-white/20 to-white/10 rounded-xl flex items-center justify-center shadow-xl backdrop-blur-md">
                    <Star className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h4 className="font-inter font-bold text-white text-sm mb-1">Førsteklasses kvalitet</h4>
                    <p className="font-inter text-white/80 text-xs">Garantert høy standard på alle prosjekter</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-auto p-6 relative z-10">
            <div className="bg-gradient-to-r from-white/15 via-white/20 to-white/15 text-white p-4 rounded-2xl shadow-2xl backdrop-blur-md border border-white/20 relative overflow-hidden">
              <div className="absolute top-0 right-0 w-16 h-16 bg-white/5 rounded-full transform translate-x-8 -translate-y-8" />

              <div className="relative z-10">
                <h3 className="font-playfair text-xl font-bold mb-2">Kontakt oss</h3>
                <p className="font-inter text-xs mb-3 text-white/90">Ring for eksklusiv konsultasjon</p>
                <div className="space-y-2 text-xs">
                  <div className="flex items-center gap-2 bg-white/10 backdrop-blur-md rounded-full px-3 py-2 border border-white/20">
                    <Phone className="w-3 h-3" />
                    <span className="font-inter font-medium">+47 123 45 678</span>
                  </div>
                  <div className="flex items-center gap-2 bg-white/10 backdrop-blur-md rounded-full px-3 py-2 border border-white/20">
                    <Mail className="w-3 h-3" />
                    <span className="font-inter font-medium"><EMAIL></span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </BrochurePanel>

        {/* Right Panel - Premium Services */}
        <BrochurePanel type="back" className="relative">
          {/* Premium gray gradient */}
          <div className="absolute inset-0 bg-gradient-to-br from-slate-700 via-slate-600 to-gray-700 opacity-95" />
          
          {/* Sophisticated background elements */}
          <div className="absolute inset-0">
            <div className="absolute top-0 left-0 w-96 h-96 bg-white/5 rounded-full transform -translate-x-48 -translate-y-48 blur-3xl" />
            <div className="absolute bottom-1/3 right-0 w-72 h-72 bg-white/5 rounded-full transform translate-x-36 blur-2xl" />
          </div>
          
          <div className="relative z-10 p-10">
            <div className="mb-10">
              <div className="flex items-center gap-4 mb-6">
                <h2 className="font-playfair text-5xl font-bold text-white">Våre tjenester</h2>
                <div className="w-12 h-2 bg-white/80 rounded-full" />
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-6 mb-12">
              <div className="group hover:scale-105 transition-transform duration-300">
                <div className="bg-white/10 backdrop-blur-md p-8 rounded-3xl text-center border border-white/20 hover:border-white/40 transition-all shadow-lg">
                  <div className="w-20 h-20 bg-gradient-to-br from-white/20 to-white/10 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-xl">
                    <TreePine className="w-10 h-10 text-white" />
                  </div>
                  <h3 className="font-inter font-bold text-white text-lg">Hagedesign</h3>
                </div>
              </div>
              
              <div className="group hover:scale-105 transition-transform duration-300">
                <div className="bg-white/10 backdrop-blur-md p-8 rounded-3xl text-center border border-white/20 hover:border-white/40 transition-all shadow-lg">
                  <div className="w-20 h-20 bg-gradient-to-br from-white/20 to-white/10 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-xl">
                    <Leaf className="w-10 h-10 text-white" />
                  </div>
                  <h3 className="font-inter font-bold text-white text-lg">Planting & Stell</h3>
                </div>
              </div>
              
              <div className="group hover:scale-105 transition-transform duration-300">
                <div className="bg-white/10 backdrop-blur-md p-8 rounded-3xl text-center border border-white/20 hover:border-white/40 transition-all shadow-lg">
                  <div className="w-20 h-20 bg-gradient-to-br from-white/20 to-white/10 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-xl">
                    <Droplets className="w-10 h-10 text-white" />
                  </div>
                  <h3 className="font-inter font-bold text-white text-lg">Vannanlegg</h3>
                </div>
              </div>
              
              <div className="group hover:scale-105 transition-transform duration-300">
                <div className="bg-white/10 backdrop-blur-md p-8 rounded-3xl text-center border border-white/20 hover:border-white/40 transition-all shadow-lg">
                  <div className="w-20 h-20 bg-gradient-to-br from-white/20 to-white/10 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-xl">
                    <Shovel className="w-10 h-10 text-white" />
                  </div>
                  <h3 className="font-inter font-bold text-white text-lg">Gravearbeider</h3>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-auto p-10 relative z-10">
            <div className="bg-gradient-to-r from-white/15 via-white/20 to-white/15 text-white p-8 rounded-3xl shadow-2xl backdrop-blur-md border border-white/20 relative overflow-hidden">
              <div className="absolute top-0 left-0 w-28 h-28 bg-white/5 rounded-full transform -translate-x-14 -translate-y-14" />
              <div className="absolute bottom-0 right-0 w-24 h-24 bg-white/5 rounded-full transform translate-x-12 translate-y-12" />
              
              <div className="relative z-10">
                <h3 className="font-playfair text-3xl font-bold mb-4">Gratis konsultasjon</h3>
                <p className="font-inter text-sm mb-8 text-white/90">Vi kommer hjem til deg og lager et eksklusivt tilbud</p>
                <Button 
                  variant="secondary" 
                  className="w-full bg-white/90 text-slate-800 hover:bg-white font-inter font-semibold py-4 rounded-2xl shadow-xl hover:shadow-2xl transition-all transform hover:scale-105"
                >
                  Bestill konsultasjon
                </Button>
              </div>
            </div>
            
            <div className="mt-8 flex items-center justify-center gap-3 text-sm text-white/80 bg-white/10 backdrop-blur-md rounded-full px-8 py-4 border border-white/20">
              <MapPin className="w-5 h-5" />
              <span className="font-inter font-medium">Dekker hele Ringerike kommune</span>
            </div>
          </div>
        </BrochurePanel>
      </div>
    </div>
  );
};

export default TriFoldBrochure;