import { ReactNode } from "react";
import { cn } from "@/lib/utils";

interface BrochurePanelProps {
  children: ReactNode;
  className?: string;
  type?: "front" | "inside" | "back";
}

const BrochurePanel = ({ children, className, type = "inside" }: BrochurePanelProps) => {
  const baseClasses = "min-h-[800px] p-8 flex flex-col justify-between";
  const typeClasses = {
    front: "bg-gradient-green text-white",
    inside: "bg-gradient-brown text-white",
    back: "bg-gradient-gray text-white"
  };

  return (
    <div className={cn(baseClasses, typeClasses[type], className)}>
      {children}
    </div>
  );
};

export default BrochurePanel;