@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    /* Landscape color palette */
    --primary: 147 73% 37%;  /* Vibrant green */
    --primary-foreground: 0 0% 100%;
    --primary-dark: 147 73% 22%;  /* Dark green */
    --primary-light: 147 73% 52%;  /* Lighter green */

    --secondary: 40 23% 45%;  /* Earth brown */
    --secondary-foreground: 0 0% 100%;

    --muted: 220 9% 46%;  /* Medium grey */
    --muted-foreground: 0 0% 100%;

    --accent: 147 73% 37%;  /* Match primary */
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 147 73% 37%;

    --radius: 0.5rem;
    
    /* Custom landscape colors */
    --nature-green: 147 73% 37%;
    --nature-green-dark: 147 73% 22%;
    --nature-green-light: 147 73% 52%;
    --earth-brown: 40 23% 45%;
    --stone-grey: 220 9% 46%;
    --charcoal: 220 13% 18%;
    
    /* Gradients */
    --gradient-nature: linear-gradient(135deg, hsl(var(--nature-green)), hsl(var(--nature-green-light)));
    --gradient-earth: linear-gradient(135deg, hsl(var(--earth-brown)), hsl(var(--stone-grey)));
    --gradient-hero: linear-gradient(135deg, hsl(var(--nature-green-dark)), hsl(var(--nature-green)));
    --gradient-green: linear-gradient(135deg, hsl(147 60% 45%), hsl(147 50% 35%));
    --gradient-brown: linear-gradient(135deg, hsl(40 35% 55%), hsl(35 30% 45%));
    --gradient-gray: linear-gradient(135deg, hsl(220 15% 35%), hsl(220 20% 25%));
    
    /* Shadows */
    --shadow-green: 0 10px 30px -10px hsl(var(--nature-green) / 0.3);
    --shadow-soft: 0 4px 20px -4px hsl(var(--charcoal) / 0.1);
    
    /* Animations */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}